// Post, create new Profile Resource
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getOrCreateActivityProfileService from '../../../../services/mssql/activity-profile/get-or-create.service.js'
import ActivityProfileModel from '../../../../models/activity-profile.model.js'
import { File } from 'formidable'
import httpStatus from 'http-status'
const { BAD_REQUEST, CONFLICT, INTERNAL_SERVER_ERROR, NO_CONTENT, PRECONDITION_FAILED } = httpStatus
import parseForm from '../../../../utils/parse-form.js'
import fsExtra from 'fs-extra'
import saveFile from '../../../../services/amqp/file/save.service.js'
import { createHash } from 'crypto'
import deleteFile from '../../../../services/amqp/file/delete.service.js'
import updateActivityProfile from '../../../../services/mssql/activity-profile/update.service.js'
import { RESOURCE_DOES_NOT_EXIST, checkModificationConditions, MISSING_CONCURRENCY_HEADERS } from '../../../../utils/etag.utils.js'
import { getErrorMessage, getErrorStackTrace, httpLogTransformer } from '@tess-f/backend-utils'
import { EtagPreconditionFail } from '../../../../utils/error.utils.js'
import deleteActivityProfile from '../../../../services/mssql/activity-profile/delete.service.js'

const log = logger.create('HTTP-Controller.Create-Activity-Profile', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    let file: File | undefined
    let base64Contents: string | undefined
    let profile: string | undefined
    const hasher = createHash('sha256')
    let etag: string | undefined

    if (req.is('multipart/*')) {
      // get the file from the request
      const { files } = await parseForm(req)
      let fileCount = 0
      for (const fileKey in files) {
        if (files[fileKey]!.length > 1) {
          fileCount += files[fileKey]!.length
        }
        fileCount++
        file = files[fileKey]![0]
      }
      if (fileCount > 1) {
        // cannot have more than 1 file
        res.status(BAD_REQUEST).send('Invalid form: request cannot contain more than 1 file')
        // remove the temp uploaded files
        for (const fileKey in files) {
          for (const f of files[fileKey]!) {
            await fsExtra.remove(f.filepath)
          }
        }
        return
      }

      if (file) {
        base64Contents = await fsExtra.readFile(file.filepath, 'base64')
        await fsExtra.remove(file.filepath)
        hasher.update(base64Contents)
        etag = hasher.digest('hex')
      }

    } else if (req.is('application/octet-stream')) {
      // payload is a buffer
      profile = (<Buffer>req.body).toString('utf-8')
      hasher.update(profile)
      etag = hasher.digest('hex')

    } else {
      profile = JSON.stringify(req.body)
      hasher.update(profile)
      etag = hasher.digest('hex')
    }

    // get or create the activity Profile record
    const { activityProfile, created } = await getOrCreateActivityProfileService(new ActivityProfileModel({
      ID: req.query.profileId!.toString(),
      ActivityID: req.query.activityId!.toString(),
      ContentType: req.headers['content-type']
    }))


    try {
      checkModificationConditions(req, activityProfile.fields.Etag, created, true)
    } catch (error) {
      if (error instanceof EtagPreconditionFail && error.message === RESOURCE_DOES_NOT_EXIST) {
        log('warn', 'Failed to create activity Profile: ETag Precondition failed with resource does not exist. Removing created Profile record', { success: false })
        await deleteActivityProfile(activityProfile.fields.ID!, activityProfile.fields.ActivityID!)
        if (activityProfile.fields.FileID) {
          await deleteFile([activityProfile.fields.FileID])
        }
      }
      throw error
    }

    // If we didn't already have a Profile let's update the one we just created with Profile data
    activityProfile.fields.Etag = etag
    if (created) {
      // this is a new Profile
      if (file && base64Contents) {
        // we have a file being uploaded
        // now that we have the content lets save it in fds
        activityProfile.fields.ContentType = file.mimetype ?? 'text/plain'
        log('verbose', 'Activity Profile is a file, uploading to FDS')
        activityProfile.fields.FileID = await saveFile(base64Contents, file.originalFilename ?? 'Profile-file.txt', activityProfile.fields.ContentType)
      } else if (profile) {
        // we have a json Profile object
        activityProfile.fields.Profile = profile
        activityProfile.fields.ContentType = req.headers['content-type'] ?? 'application/json'
      }
    } else if (file && base64Contents) {
      // we are updating the Profile with a new file
      if (activityProfile.fields.FileID) {
        // we had a file, let's remove it
        log('verbose', 'Activity has new file, removing previous file from FDS')
        await deleteFile([activityProfile.fields.FileID])
      }
      activityProfile.fields.Profile = null // wipe out previous JSON Profile if it had one
      activityProfile.fields.ContentType = file.mimetype ?? 'text/plain'
      log('verbose', 'Saving activity Profile file in FDS')
      activityProfile.fields.FileID = await saveFile(base64Contents, file.originalFilename ?? 'Profile-file.txt', activityProfile.fields.ContentType)
    } else if (profile) {
      // we are updating the Profile with a new json object
      // we need to merge the old Profile with the new Profile
      let originalProfile: any = {}
      if (activityProfile.fields.Profile) {
        originalProfile = JSON.parse(activityProfile.fields.Profile)
      }
      const keys = Object.keys(originalProfile).concat(Object.keys(req.body))
      // merge the objects together
      const merged: any = {}
      for (const key of keys) {
        // if the incoming has the value use it
        if (req.body[key]) {
          merged[key] = req.body[key]
        } else if (originalProfile[key]) {
          // if the key is in the original but not the incoming
          // use the value of the original Profile
          merged[key] = originalProfile[key]
        }
      }
      activityProfile.fields.Profile = JSON.stringify(merged)
      const updateHasher = createHash('sha256')
      updateHasher.update(activityProfile.fields.Profile)
      activityProfile.fields.Etag = updateHasher.digest('hex')
      activityProfile.fields.ContentType = 'application/json'
    }

    if (req.header('updated') && Date.parse(req.header('updated')!)) {
      activityProfile.fields.ModifiedOn = new Date(req.header('updated')!)
    } else {
      activityProfile.fields.ModifiedOn = new Date()
    }

    // update the activity Profile
    await updateActivityProfile(activityProfile)
    log('info', 'Successfully set activity Profile', {
      profileId: activityProfile.fields.ID,
      activityId: activityProfile.fields.ActivityID,
      success: true
    })

    res.sendStatus(NO_CONTENT)
  } catch (error) {
    if (error instanceof EtagPreconditionFail && error.message === MISSING_CONCURRENCY_HEADERS) {
      log('warn', 'Failed to create activity profile: missing concurrency headers for existing resource', { errorMessage: error.message, success: false })
      res.status(CONFLICT).send(error.message)
    } else if (error instanceof EtagPreconditionFail) {
      log('warn', 'Failed to save activity Profile: Etag precondition failed', { errorMessage: error.message, success: false })
      res.status(PRECONDITION_FAILED).send(error.message)
    } else {
      log('error', 'Failed to save activity Profile: unknown error', { errorMessage: getErrorMessage(error), errorStack: getErrorStackTrace(error), success: false })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
