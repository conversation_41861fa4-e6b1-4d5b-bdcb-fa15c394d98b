// Post, create new State Resource
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getOrCreateAgent from '../../../../services/mssql/agent/get-or-create.service.js'
import getOrCreateActivityStateService from '../../../../services/mssql/activity-state/get-or-create.service.js'
import AgentModel from '../../../../models/agents.model.js'
import ActivityStateModel from '../../../../models/activity-state.model.js'
import { File } from 'formidable'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, NO_CONTENT, PRECONDITION_FAILED, CONFLICT } = httpStatus
import parseForm from '../../../../utils/parse-form.js'
import fsExtra from 'fs-extra'
import saveFile from '../../../../services/amqp/file/save.service.js'
import { createHash } from 'crypto'
import deleteFile from '../../../../services/amqp/file/delete.service.js'
import updateActivityState from '../../../../services/mssql/activity-state/update.service.js'
import { getErrorMessage, getErrorStackTrace, httpLogTransformer } from '@tess-f/backend-utils'
import { EtagPreconditionFail } from '../../../../utils/error.utils.js'
import { checkModificationConditions, RESOURCE_DOES_NOT_EXIST, MISSING_CONCURRENCY_HEADERS } from '../../../../utils/etag.utils.js'
import deleteActivityState from '../../../../services/mssql/activity-state/delete.service.js'

const log = logger.create('HTTP-Controller.Create-Activity-State', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    let file: File | undefined
    let base64Contents: string | undefined
    let state: string | undefined
    const hasher = createHash('sha256')
    let etag: string | undefined

    if (req.is('multipart/*')) {
      // get the file from the request
      const { files } = await parseForm(req)
      let fileCount = 0
      for (const fileKey in files) {
        if (files[fileKey]!.length > 1) {
          fileCount += files[fileKey]!.length
        }
        fileCount++
        file = files[fileKey]![0]
      }
      if (fileCount > 1) {
        // cannot have more than 1 file
        res.status(BAD_REQUEST).send('Invalid form: request cannot contain more than 1 file')
        // remove the temp uploaded files
        for (const fileKey in files) {
          for (const f of files[fileKey]!) {
            await fsExtra.remove(f.filepath)
          }
        }
        return
      }

      if (file) {
        base64Contents = await fsExtra.readFile(file.filepath, 'base64')
        await fsExtra.remove(file.filepath)
        hasher.update(base64Contents)
        etag = hasher.digest('hex')
      }

    } else {
      state = JSON.stringify(req.body)
      hasher.update(state)
      etag = hasher.digest('hex')
    }

    // get or create the agent
    const agent = await getOrCreateAgent(new AgentModel(JSON.parse(req.query.agent!.toString())))

    // get or create the activity state record
    const { activityState, created } = await getOrCreateActivityStateService(new ActivityStateModel({
      ID: req.query.stateId!.toString(),
      AgentID: agent.ID,
      ActivityID: req.query.activityId!.toString(),
      ContentType: req.headers['content-type'],
      RegistrationID: req.query.registration?.toString()
    }))
  
    // The State Resource will permit PUT, POST and DELETE requests without concurrency headers, since state conflicts are unlikely
    try {
      checkModificationConditions(req, activityState.fields.Etag, created, true, true)
    } catch (error) {
      if (error instanceof EtagPreconditionFail && error.message === RESOURCE_DOES_NOT_EXIST) {
        log('warn', 'Failed to create activity state: ETag Precondition failed with resource does not exist. Removing created state record', { success: false })
        await deleteActivityState(activityState.fields.ID!, activityState.fields.AgentID!, activityState.fields.ActivityID!, activityState.fields.RegistrationID)
        if (activityState.fields.FileID) {
          await deleteFile([activityState.fields.FileID])
        }
      }
      throw error
    }

    // If we didn't already have a state let's update the one we just created with state data
    activityState.fields.Etag = etag
    if (created) {
      // this is a new state
      if (file && base64Contents) {
        // we have a file being uploaded
        // now that we have the content lets save it in fds
        activityState.fields.ContentType = file.mimetype ?? 'text/plain'
        log('verbose', 'Activity state is a file, uploading to FDS')
        activityState.fields.FileID = await saveFile(base64Contents, file.originalFilename ?? 'state-file.txt', activityState.fields.ContentType)
      } else if (state) {
        // we have a json state object
        activityState.fields.State = state
        activityState.fields.ContentType = 'application/json'
      }
    } else if (file && base64Contents) {
      // we are updating the state with a new file
      if (activityState.fields.FileID) {
        // we had a file, let's remove it
        log('verbose', 'Activity has new file, removing previous file from FDS')
        await deleteFile([activityState.fields.FileID])
      }
      activityState.fields.State = null // wipe out previous JSON state if it had one
      activityState.fields.ContentType = file.mimetype ?? 'text/plain'
      log('verbose', 'Saving activity state file in FDS')
      activityState.fields.FileID = await saveFile(base64Contents, file.originalFilename ?? 'state-file.txt', activityState.fields.ContentType)
    } else if (state && req.get('content-type') === 'application/json') {
      // we are updating the state with a new json object
      // we need to merge the old state with the new state
      let originalState: any = {}
      if (activityState.fields.State) {
        originalState = JSON.parse(activityState.fields.State)
      }
      const keys = Object.keys(originalState).concat(Object.keys(req.body))
      // merge the objects together
      const merged: any = {}
      for (const key of keys) {
        // if the incoming has the value use it
        if (req.body[key]) {
          merged[key] = req.body[key]
        } else if (originalState[key]) {
          // if the key is in the original but not the incoming
          // use the value of the original state
          merged[key] = originalState[key]
        }
      }
      activityState.fields.State = JSON.stringify(merged)
      const updateHasher = createHash('sha256')
      updateHasher.update(activityState.fields.State)
      activityState.fields.Etag = updateHasher.digest('hex')
      activityState.fields.ContentType = 'application/json'
    } else if (state) {
      // we have new state that is not a file and is not json
      activityState.fields.State = state
      activityState.fields.ContentType = req.get('content-type') ?? 'text/plain'
    }

    if (req.header('updated') && Date.parse(req.header('updated')!)) {
      activityState.fields.ModifiedOn = new Date(req.header('updated')!)
    } else {
      // For updates, ensure the new timestamp is always greater than the existing one
      const now = new Date()
      const existingTimestamp = activityState.fields.ModifiedOn

      if (!created && existingTimestamp) {
        // For existing records, always ensure the new timestamp is at least 1ms newer
        const minNewTime = existingTimestamp.getTime() + 1
        const newTime = Math.max(now.getTime(), minNewTime)
        activityState.fields.ModifiedOn = new Date(newTime)
      } else {
        // For new records, use current time
        activityState.fields.ModifiedOn = now
      }
    }

    // update the activity state
    const updatedActivityState = await updateActivityState(activityState)
    log('info', 'Successfully set activity state', {
      stateId: updatedActivityState.fields.ID,
      activityId: updatedActivityState.fields.ActivityID,
      agentId: updatedActivityState.fields.AgentID,
      registration: updatedActivityState.fields.RegistrationID,
      success: true,
      req
    })

    // Set Last-Modified header to indicate when the document was updated
    if (updatedActivityState.fields.ModifiedOn) {
      res.setHeader('Last-Modified', updatedActivityState.fields.ModifiedOn.toISOString())
    }
    res.sendStatus(NO_CONTENT)
  } catch (error) {
    if (error instanceof EtagPreconditionFail && error.message === MISSING_CONCURRENCY_HEADERS) {
      log('warn', 'Failed to create activity state: missing concurrency headers for existing resource', { errorMessage: error.message, success: false, req })
      res.status(CONFLICT).send(error.message)
    } else if (error instanceof EtagPreconditionFail) {
      log('warn', 'Failed to save activity state: Etag precondition failed', { errorMessage: error.message, success: false, req })
      res.status(PRECONDITION_FAILED).send(error.message)
    } else {
      log('error', 'Failed to save activity state: unknown error', { errorMessage: getErrorMessage(error), errorStack: getErrorStackTrace(error), success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
