import { expect } from 'chai'
import httpStatus from 'http-status'
const { BAD_REQUEST, OK } = httpStatus
import httpMocks from 'node-mocks-http'
import Sinon from 'sinon'
import middleware from './xapi-version-header.middleware.js'
import settings from '../../../config/settings.js'

describe('HTTP Middleware: XAPI Header', () => {
  afterEach(() => Sinon.restore())

  it('should reject requests with missing "X-Experience-API-Version" header.', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()
    middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.false
    const temp = mocks.res._getData()
    console.log(temp)
    expect(mocks.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks.res._getData()).to.eq('X-Experience-API-Version header missing')
    expect(mocks.res.statusCode).to.eq(BAD_REQUEST)
  })

  it('should include the "X-Experience-API-Version" header in every response.', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks({ headers: { 'X-Experience-API-Version': '2.0.0' } })
    middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.true
    expect(mocks.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks.res.statusCode).to.eq(OK)
  })

  it('should accept requests with xAPI 2.0.0 version header', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks({ headers: { 'X-Experience-API-Version': '2.0.0' } })
    middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.true
    expect(mocks.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks.res.statusCode).to.eq(OK)
  })
})
