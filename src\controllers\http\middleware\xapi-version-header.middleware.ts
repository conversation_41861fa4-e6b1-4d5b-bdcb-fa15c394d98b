import { Request, Response, NextFunction } from 'express'
import settings from '../../../config/settings.js'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus

export default function xApiProcessVersionHeader(req: Request, res: Response, next: NextFunction) {
  res.setHeader('X-Experience-API-Version', settings.XAPI_VERSION)
  let version = req.get('X-Experience-API-Version') ?? req.get('HTTP_X_EXPERIENCE_API_VERSION') ?? req.get('X_Experience_API_Version');

  // check for content type 'application/x-www-form-urlencoded'
  if (req.get('content-type')?.includes('application/x-www-form-urlencoded') && req.body) {
    version = req.body['X-Experience-API-Version'] ?? req.body['X_Experience_API_Version'] ?? version
  }

  if (!version) {
    res.status(BAD_REQUEST).send('X-Experience-API-Version header missing')
    return
  }

  if (!settings.XAPI_VERSIONS.includes(version)) {
    res.status(BAD_REQUEST).send('X-Experience-API-Version is not supported')
    return
  }

  next()
}