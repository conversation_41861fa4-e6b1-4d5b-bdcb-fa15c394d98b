import logger from '@lcs/logger'
import { Request, Response, NextFunction } from 'express'
import parseForm from '../../../../utils/parse-form.js'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus
import { File } from 'formidable'
import validate from '../../../../services/validators/statement/validate.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import { StatementJson } from '../../../../models/statement.model.js'
import pkg from 'fs-extra'
const { removeSync, readFileSync } = pkg
import jws from 'jws'

const log = logger.create('HTTP-Middleware:multipart-mixed-parser', httpLogTransformer)

export default async function multiPartMixedParser(req: Request, res: Response, next: NextFunction) {
  if (!req.is('multipart/mixed') && req.get('content-type') !== 'multipart/mixed') {
    next()
    return
  }
  // this is a multi part mixed form
  // there should be application/json data as the first part of the form
  const { files, headers } = await parseForm(req)
  const allFiles: Array<File & { headers: { [key: string]: string } }> = []

  for (const fileKey in files) {
    files[fileKey]!.forEach(file => {
      if (headers.has(fileKey)) {
        allFiles.push({ ...file, headers: headers.get(fileKey)! })
      } else {
        allFiles.push({ ...file, headers: {} })
      }
    })
  }

  if (allFiles.length <= 0) {
    log('warn', 'Content type was multipart/mixed but no files found', { success: false, req })
    res.status(BAD_REQUEST).send('No data provided')
    return
  }

  const firstFile = allFiles[0]
  allFiles.shift()!

  // Try to parse the first file as JSON regardless of mimetype
  // This is more robust for multipart/mixed requests where the first part should always be JSON
  // according to xAPI specification for signed statements
  try {
    const fileContent = readFileSync(firstFile.filepath, 'utf-8')
    req.body = JSON.parse(fileContent)
    removeFiles([firstFile])
  } catch (error) {
    log('warn', 'First file in request could not be parsed as JSON', {
      mimetype: firstFile.mimetype,
      headers: firstFile.headers,
      errorMessage: getErrorMessage(error),
      success: false,
      req
    })
    res.status(BAD_REQUEST).send('Missing request data')
    removeFiles([...allFiles, firstFile])
    return
  }

  try {
    validate(req.body)
  } catch (error) {
    // statement body not valid
    const errorMessage = getErrorMessage(error)
    log('warn', 'JSON body was not a valid statement or batch of statements', { success: false, errorMessage, req })
    res.status(BAD_REQUEST).send(errorMessage)
    removeFiles(allFiles)
    return
  }

  // check the remaining files against their sha2 hashes
  const statementSha2s = getStatementSha2s(req.body)

  for (const file of allFiles) {
    const isValid = validateFileHash(file, statementSha2s)
    if (!isValid.valid) {
      log('warn', 'File is not valid', { errorMessage: isValid.message, success: false, req })
      res.status(BAD_REQUEST).send(isValid.message)
      removeFiles(allFiles)
      return
    }
  }

  // check that all attachments have a sha2
  const fileShas = getFileSha2s(req.body)
  if (fileShas.some(sha => !allFiles.some(file => file.hash === sha))) {
    log('warn', 'File attachment missing in the request.', { success: false, req })
    res.status(BAD_REQUEST).send('Missing file in the request')
    return
  }

  // get signatures
  const signatureShas = getStatementSha2s(req.body, true)

  for (const signatureSha of signatureShas) {
    // get the file
    const file = allFiles.find(f => f.hash === signatureSha)
    if (file) {
      const isValid = validateSignature(file)
      if (!isValid.valid) {
        log('warn', 'Invalid JWS signature', { errorMessage: isValid.message, success: false, req })
        res.status(BAD_REQUEST).send(isValid.message)
        removeFiles(allFiles)
        return
      }
    }
  }

  req.files = allFiles
  next()
}

function removeFiles(files: File[]) {
  files.forEach(file => removeSync(file.filepath))
}

function getStatementSha2s(statements: StatementJson | StatementJson[], signaturesOnly: boolean = false): string[] {
  const sha2: string[] = []
  if (Array.isArray(statements)) {
    statements.forEach(statement => {
      if (statement.attachments) {
        statement.attachments.forEach(attachment => {
          if ((signaturesOnly && attachment.usageType?.includes('://adlnet.gov/expapi/attachments/signature')) || !signaturesOnly) {
            sha2.push(attachment.sha2!)
          }
        })
      }
    })
  } else if (statements.attachments) {
    statements.attachments.forEach(attachment => {
      if ((signaturesOnly && attachment.usageType?.includes('://adlnet.gov/expapi/attachments/signature')) || !signaturesOnly) {
        sha2.push(attachment.sha2!)
      }
    })
  }
  return sha2
}

function getFileSha2s(statements: StatementJson | StatementJson[]): string[] {
  const sha2: string[] = []
  if (Array.isArray(statements)) {
    statements.forEach(statement => {
      if (statement.attachments) {
        statement.attachments.forEach(attachment => {
          if (!attachment.fileUrl) {
            sha2.push(attachment.sha2!)
          }
        })
      }
    })
  } else if (statements.attachments) {
    statements.attachments.forEach(attachment => {
      if (!attachment.fileUrl) {
        sha2.push(attachment.sha2!)
      }
    })
  }
  return sha2
}

function validateFileHash(file: File & { headers: { [key: string]: string } }, statementSha2s: string[]): { valid: boolean, message: string } {
  // all headers get returned with the key in lowercase
  const hashHeader = file.headers['x-experience-api-hash'] ?? file.headers['X-Experience-API-Hash']
  if (!hashHeader) {
    return { valid: false, message: 'X-Experience-API-Hash header was missing from attachment' }
  }

  if (hashHeader !== file.hash) {
    return { valid: false, message: 'Hash header did not match calculated hash' }
  }

  if (!statementSha2s.includes(file.hash)) {
    return { valid: false, message: 'Attachment not found in statement payload attachments' }
  }

  return { valid: true, message: 'file hash valid' }
}

function validateSignature(file: File): { valid: boolean, message: string } {
  const token = readFileSync(file.filepath).toString()
  const decodedToken = jws.decode(token)

  // validate signature of JWS
  if (!jws.isValid(token)) {
    return { valid: false, message: 'JWS signature invalid ' }
  }

  if (decodedToken === null) {
    return { valid: false, message: 'JWS signature invalid' }
  }

  // validate payload of JWS, (JWS signature MUST have a payload of a valid JSON)
  try {
    JSON.parse(decodedToken.payload).toString()
  }
  catch (error) {
    return { valid: false, message: 'JWS payload is Invalid' }
  }

  // Rejects signed statement with another algorithm other than RS256, RS384 and RS512
  const validAlgorithm = [jws.ALGORITHMS[3].toString(), jws.ALGORITHMS[4].toString(), jws.ALGORITHMS[5].toString()]
  if (!validAlgorithm.includes(decodedToken.header.alg)) {
    return { valid: false, message: 'JWS algorithm other than RS256, RS384 and RS512' }
  }

  return { valid: true, message: 'JWS file signature valid' }
}