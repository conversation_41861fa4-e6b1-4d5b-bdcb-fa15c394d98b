import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import logger from '@lcs/logger'
import { shouldAllowAlternateRequestSyntax, isUsingAlternateRequestSyntax } from '../../../utils/xapi-version.utils.js'

describe('HTTP Controller: PUT Statement - xAPI Version Utils', () => {
  before(() => logger.init({ level: 'silly' }))

  describe('xAPI Version Utilities', () => {
    it('should detect alternate request syntax', () => {
      const mocks = httpMocks.createMocks({
        headers: {
          'content-type': 'application/x-www-form-urlencoded'
        }
      })

      expect(isUsingAlternateRequestSyntax(mocks.req)).to.be.true
    })

    it('should not detect alternate request syntax for JSON', () => {
      const mocks = httpMocks.createMocks({
        headers: {
          'content-type': 'application/json'
        }
      })

      expect(isUsingAlternateRequestSyntax(mocks.req)).to.be.false
    })

    it('should not allow alternate request syntax for xAPI 2.0', () => {
      const mocks1 = httpMocks.createMocks({
        headers: {
          'X-Experience-API-Version': '2.0.0'
        }
      })

      expect(shouldAllowAlternateRequestSyntax(mocks1.req)).to.be.false
    })

    it('should allow alternate request syntax for xAPI versions other than 2.0 (theoretical)', () => {
      // Note: This LRS only supports xAPI 2.0, but the function should still handle other versions correctly
      const mocks1 = httpMocks.createMocks({
        headers: {
          'X-Experience-API-Version': '1.0.3'
        }
      })

      expect(shouldAllowAlternateRequestSyntax(mocks1.req)).to.be.true
    })

    it('should allow alternate request syntax when no version header is present (defaults to non-2.0 behavior)', () => {
      const mocks = httpMocks.createMocks({})
      expect(shouldAllowAlternateRequestSyntax(mocks.req)).to.be.true
    })
  })
})
