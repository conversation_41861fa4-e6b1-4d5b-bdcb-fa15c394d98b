import { Request, Response } from 'express'
import errors from '../../../config/errors.js'
import StatementModel, { StatementJson } from '../../../models/statement.model.js'
import createStatement from '../../../services/mssql/statements/create.service.js'
import logger from '@lcs/logger'
import validate, { validateVoidStatement } from '../../../services/validators/statement/validate.service.js'
import httpStatus from 'http-status'
const { BAD_REQUEST, CONFLICT, INTERNAL_SERVER_ERROR, NO_CONTENT } = httpStatus
import getStatementById from '../../../services/mssql/statements/get-by-id.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { StatementRef } from '../../../models/sub-statement.model.js'
import voidStatement from '../../../services/mssql/statements/void.service.js'
import pkg from 'fs-extra';
const { readFileSync, removeSync } = pkg;
import saveFile from '../../../services/amqp/file/save.service.js'
import updateStatementAttachment from '../../../services/mssql/attachments/update.service.js'
import { getErrorMessage, getErrorStackTrace, httpLogTransformer } from '@tess-f/backend-utils'
import { shouldAllowAlternateRequestSyntax, isUsingAlternateRequestSyntax } from '../../../utils/xapi-version.utils.js'

const log = logger.create('HTTP-Controller.put-statement', httpLogTransformer)

export default async function (req: Request, res: Response) {
  if (Array.isArray(req.body)) {
    res.status(httpStatus.BAD_REQUEST).send('Request body should be a single statement, use POST method to send multiple statements.')
    log('warn', 'Failed to create statement, bad request: request body was an array', { req, success: false })
    return
  }

  // Check if alternate request syntax is being used and should be rejected for xAPI 2.0
  if (isUsingAlternateRequestSyntax(req) && !shouldAllowAlternateRequestSyntax(req)) {
    res.status(BAD_REQUEST).send('Alternate request syntax is not supported in xAPI 2.0')
    log('warn', 'Failed to create statement: alternate request syntax not allowed in xAPI 2.0', { req, success: false })
    return
  }

  // The Learning Record Provider MUST NOT include any other query string parameters on the request.
  if (Object.keys(req.query).length > 1) {
    res.status(httpStatus.BAD_REQUEST).send('Extra parameters in query string.')
    log('warn', 'Failed to create statement. Bad request: query string contains extra parameters', { req, success: false })
    return
  }

  // statementId could come from query or body, and body could contain a statement or statementId and content
  let statementId = req.query.statementId
  let statementBody = req.body

  try {
    if (isUsingAlternateRequestSyntax(req) && req.body.content) {
      statementId = req.body.statementId.toString()
      statementBody = JSON.parse(req.body.content)
    }
    validate(statementBody)

  } catch (error: unknown) {
    let errorMessage = 'validation error'
    if (error instanceof Error) {
      errorMessage = error.message
    }
    log('warn', 'Failed to save statement: validation error', { errorMessage, success: false, req })
    res.status(BAD_REQUEST).send(errorMessage)
    return
  }

  if (!statementId || typeof statementId !== 'string') {
    log('warn', 'Failed to save statement: missing statementId query param or query param is not a string', { success: false, req })
    res.status(BAD_REQUEST).send('statementId query parameter is required when PUTing a statement')
    return
  }

  const reqBody: StatementJson = statementBody
  if (!reqBody.authority) {
    reqBody.authority = req.authority
  }

  const statement: StatementModel = new StatementModel(reqBody)
  statement.fields.id = statementId

  try {
    // check for conflicting statement
    if (statement.fields.id) {
      try {
        await getStatementById(statement.fields.id)
        throw new Error(errors.STATEMENT_CONFLICT)
      } catch (error) {
        // if not found we are good to go
        if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
          throw error
        }
      }
    }

    if (statement.fields.verb?.id.includes('://adlnet.gov/expapi/verbs/voided')) {
      // validate void statement
      await validateVoidStatement((statement.fields.object as StatementRef).id)
    }

    const created = await createStatement(statement)

    if (statement.fields.verb?.id.includes('://adlnet.gov/expapi/verbs/voided')) {
      // void the requested statement, if the statement to void doesn't exist that's ok
      const statementToVoid = (statement.fields.object as StatementRef).id
      await voidStatement(statementToVoid)
      log('info', 'Successfully voided statement', { voided: statementToVoid, success: true, req })
    }

    log('info', 'Successfully put statement', { req, success: true })
    res.sendStatus(NO_CONTENT)

    // process attachments
    if (req.files && req.files.length > 0) {
      // we have files we need to save the files and update the attachment records
      try {
        for (const file of req.files) {
          // find the attachment
          const attachment = created.attachments.find(attach => attach.fields.Sha2 === file.hash)
          if (attachment) {
            // save the file in FDS
            try {
              const fileContent = readFileSync(file.filepath, 'base64')
              attachment.fields.FileId = await saveFile(fileContent, file.originalFilename ?? 'statement-attachment', attachment.fields.ContentType!)
              // update the attachment
              await updateStatementAttachment(attachment)
            } catch (error) {
              // silent error
              log('error', 'Failed to save file and update statement', { errorMessage: getErrorMessage(error), errorStack: getErrorStackTrace(error), success: false, req })
            }
          } else {
            log('warn', 'Failed to save file no matching attachment record found', { success: false, req })
          }
          // delete the file now that we are done with it
          removeSync(file.filepath)
        }
      } catch (error) {
        // silent failure
        log('error', 'Failed to process statement attachments', { errorMessage: getErrorMessage(error), errorStack: getErrorStackTrace(error), success: false, req })
      }
    }
  } catch (error) {
    if (error instanceof Error && error.message === errors.STATEMENT_CONFLICT) {
      log('warn', 'Failed to put the statement because it conflicted with an existing statement', { req, success: false })
      res.sendStatus(CONFLICT)
    } else {
      log('error', 'Failed to put the statement', { req, errorMessage: getErrorMessage(error), success: false })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
