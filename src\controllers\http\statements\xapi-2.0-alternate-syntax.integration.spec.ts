import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus
import xApiVersionHeaderMiddleware from '../middleware/xapi-version-header.middleware.js'

describe('xAPI 2.0 Alternate Request Syntax Integration Test', () => {
  before(() => logger.init({ level: 'silly' }))

  it('should allow regular JSON requests for xAPI 2.0', () => {
    const mocks = httpMocks.createMocks({
      method: 'PUT',
      headers: {
        'content-type': 'application/json',
        'X-Experience-API-Version': '2.0.0'
      },
      body: {
        actor: {
          objectType: 'Agent',
          name: 'xAPI mbox',
          mbox: 'mailto:<EMAIL>'
        },
        verb: {
          id: 'http://adlnet.gov/expapi/verbs/attended',
          display: {
            'en-GB': 'attended',
            'en-US': 'attended'
          }
        },
        object: {
          objectType: 'Activity',
          id: 'http://www.example.com/meetings/occurances/34534'
        },
        id: 'a06a79fb-2521-4087-a561-df48c697c7e4'
      }
    })

    let nextCalled = false
    const next = () => { nextCalled = true }

    xApiVersionHeaderMiddleware(mocks.req, mocks.res, next)

    // The middleware should allow the request and call next()
    expect(nextCalled).to.be.true
    expect(mocks.res.statusCode).to.equal(200) // Default OK status
  })
})
