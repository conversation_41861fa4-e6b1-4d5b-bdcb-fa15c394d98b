import rabbitMQ from '@lcs/rabbitmq'
import settings from '../../config/settings.js'
import logger from '@lcs/logger'

const log = logger.create('Service-AMQP.broadcast-statement-event')

// Timeout wrapper for AMQP operations
// was causing issues with concurrency 
function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) =>
      setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs)
    )
  ])
}

export default async function (route: string, body: any) {
  try {
    // Add 5 second timeout to prevent blocking statement creation
    // Needed for timeout behavior for 2.0
    await withTimeout(
      rabbitMQ.publish(settings.amqp.statementExchange.name, body, route),
      5000
    )
    log('info', 'Successfully broadcast statement event', { route, success: true })
  } catch (error) {
    // Log error but don't throw - statement creation should not fail due to broadcast issues
    log('warn', 'Failed to broadcast statement event', {
      route,
      success: false,
      errorMessage: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
