import { expect } from 'chai'
import { NO_RESOURCE_MATCH, RESOURCE_DETECTED_ERROR, RESOURCE_DOES_NOT_EXIST, MISSING_CONCURRENCY_HEADERS, checkModificationConditions } from './etag.utils.js'
import { EtagPreconditionFail } from './error.utils.js'
import httpMocks from 'node-mocks-http'
import { fail } from 'assert'

describe('ETag Utils', () => {
  describe('Check Modification Conditions', () => {

    it('should return void if match not required', () => {
      const mock = httpMocks.createMocks({})
      expect(checkModificationConditions(mock.req, undefined, false, false, true)).to.equal(void 0)
    })

    it('should throw error when the record is being updated and no match header is provided', () => {
      const mock = httpMocks.createMocks({ method: 'PUT' })
      try {
        checkModificationConditions(mock.req, 'hash', false, true, true)
        fail('should throw error for existing resource without concurrency headers')
      } catch (error: any) {
        expect(error).to.exist
        expect(error instanceof EtagPreconditionFail).to.be.true
        expect(error.message).to.equal(MISSING_CONCURRENCY_HEADERS)
      }
    })


    it('should pass when updating and if none match is a wildcard for state resources', () => {
      const mock = httpMocks.createMocks({ method: 'PUT', headers: { 'if-none-match': '*' } })
      // For state resources, etag validation should be bypassed when allowStateResourceWithoutEtag is true
      expect(() => {
        checkModificationConditions(mock.req, 'hash', false, true, true)
      }).to.not.throw()
    })

    it('should throw error when updating and etag hash matches if-none-match header for existing resource', () => {
      const mock = httpMocks.createMocks({ method: 'PUT', headers: { 'if-none-match': 'hash' } })
      try {
        checkModificationConditions(mock.req, 'hash', false, true, true)
        fail('should throw error when etag matches if-none-match for existing resource')
      } catch (error: any) {
        expect(error).to.exist
        expect(error instanceof EtagPreconditionFail).to.be.true
        expect(error.message).to.equal(RESOURCE_DETECTED_ERROR)
      }
    })

    it('should throw an error when if-match header is provided and the record did not previously exist', () => {
      const mock = httpMocks.createMocks({ method: 'POST', headers: { 'if-match': '*' } })
      expect(() => {
        checkModificationConditions(mock.req, 'hash', false, true, true)
      }).to.not.throw()
    })

    it('should throw an error when if-match header does not match internal etag hash', () => {
      const mock = httpMocks.createMocks({ method: 'POST', headers: { 'if-match': 'abcd' } })
      try {
        checkModificationConditions(mock.req, 'hash', false)
        fail('should not pass')
      } catch (error: any) {
        expect(error).to.exist
        expect(error instanceof EtagPreconditionFail).to.be.true
        expect(error.message).to.equal(NO_RESOURCE_MATCH)
      }
    })

    it('should allow PUT requests without etag headers for state resources (new resource)', () => {
      const mock = httpMocks.createMocks({ method: 'PUT' })
      expect(() => {
        checkModificationConditions(mock.req, undefined, true, true, true)
      }).to.not.throw()
    })

    it('should throw error for PUT requests without etag headers for existing state resources', () => {
      const mock = httpMocks.createMocks({ method: 'PUT' })
      try {
        checkModificationConditions(mock.req, 'hash', false, true, true)
        fail('should throw error for existing resource without concurrency headers')
      } catch (error: any) {
        expect(error).to.exist
        expect(error instanceof EtagPreconditionFail).to.be.true
        expect(error.message).to.equal(MISSING_CONCURRENCY_HEADERS)
      }
    })

    it('should enforce etag validation for profile resources (allowStateResourceWithoutEtag is false)', () => {
      const mock = httpMocks.createMocks({ method: 'PUT' })
      try {
        checkModificationConditions(mock.req, 'hash', false, true, false)
        fail('should throw error for existing profile resource without concurrency headers')
      } catch (error: any) {
        expect(error).to.exist
        expect(error instanceof EtagPreconditionFail).to.be.true
        expect(error.message).to.equal(MISSING_CONCURRENCY_HEADERS)
      }
    })
  })
})
